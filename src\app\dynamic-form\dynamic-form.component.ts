import { Component, Input, OnInit, OnDestroy, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl, AbstractControl } from "@angular/forms";
import { MetadataService } from '../services/metadata.service'
import { HttpClient } from "@angular/common/http";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { environment } from '../../environments/environment';
import Choices from 'choices.js';

// Component imports
import { InitialInputComponent } from './components/initial-input/initial-input.component';
import { FormHeaderComponent } from './components/form-header/form-header.component';
import { RegularFieldComponent } from './components/regular-field/regular-field.component';
import { FormActionsComponent } from './components/form-actions/form-actions.component';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

@Component({
  selector: 'app-dynamic-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InitialInputComponent,
    FormHeaderComponent,
    RegularFieldComponent,
    FormActionsComponent,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule,
    MatExpansionModule,
    MatTableModule,
    MatChipsModule,
    MatAutocompleteModule
  ],
  templateUrl: './dynamic-form.component.html',
  styleUrl: './dynamic-form.component.scss'
})
export class DynamicFormComponent implements OnInit, OnDestroy, AfterViewInit {
  columnCount = 1;
  columns: any[][] = [];
  @Input() tableName!: string;
  @Input() screenName!: string; // Add support for screen name
  @Input() data: any;
  @Output() dataChange = new EventEmitter<any>();
  @ViewChild('mySelect') mySelect!: ElementRef;
  @ViewChild('formActions') formActions!: FormActionsComponent;

  form!: FormGroup;
  fields: any[] = [];
  submissionSuccess = false;
  errorMessage = "";
  isLoading = false;
  showInitialInput = true;
  isViewMode = false;
  successMessage: string = "";
  showSuccessPopup = false;
  showValidation: boolean = false;
  validationResult: any;
  isTenantBasedFlag: boolean = false;
  authorizeNumber = 1;
  isRowView: boolean = false; // Toggle between nested view and row view




  private metadataService = inject(MetadataService);
  private fb = inject(FormBuilder);
  private http = inject(HttpClient);
  constructor() { }

  ngOnInit() {
    if (this.tableName || this.screenName) {
      this.initializeForm();
    }
  }

  ngOnDestroy() {
    // Cleanup handled by individual components
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.fields.forEach((field) => {
        if (field.foreginKey) {
          const selectElement = document.getElementById(field.fieldName);
          if (selectElement) {
            new Choices(selectElement, {
              searchEnabled: true,
              itemSelectText: '',
            });
          }
        }
      });
    }, 200);
  }

  initializeForm() {
    this.form = this.fb.group({
      ID: ["", Validators.required],
    });
  }

  setFormReadonly(isReadonly: boolean) {
    const disableControls = (control: AbstractControl) => {
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach(disableControls);
      } else if (control instanceof FormArray) {
        control.controls.forEach(disableControls);
      } else {
        if (isReadonly) {
          control.disable({ emitEvent: false });
        } else {
          control.enable({ emitEvent: false });
        }
      }
    };

    disableControls(this.form); // Apply disable logic to the entire form

    // Explicitly handle isMulti fields
    this.fields.forEach((field) => {
      if (field.isMulti) {
        const formArray = this.form.get(field.fieldName) as FormArray;
        if (formArray) {
          formArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }

      // Explicitly handle grouped fields
      if (field.Group) {
        const groupArray = this.getGroupArray(field.Group);
        if (groupArray) {
          groupArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }
    });
  }

  viewData() {
    const idValue = this.form.get('ID')?.value;
    if (!idValue || idValue.trim() === '') {
      this.showValidation = true;
      return;
    }

    this.isViewMode = true;
    this.loadDataAndBuildForm();
    setTimeout(() => {
      this.setFormReadonly(true);
    }, 0);
  }

  loadDataAndBuildForm() {
    const idValue = this.form.get('ID')?.value;
    if (!idValue || idValue.trim() === '') {
      this.showValidation = true;
      return;
    }

    this.isLoading = true;
    this.errorMessage = "";
    this.successMessage = "";
    const id = this.form.get("ID")?.value;
    const tableNameForValidation = this.screenName ? this.screenName.split(',')[0] : this.tableName;
    const apiUrl = `${environment.baseURL}/api/validation/validate-id?tableName=${tableNameForValidation}&id=${id}`;

    this.http.get(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (response.success) {
          this.showInitialInput = false;
          this.loadTableMetadata();
        } else {
          this.errorMessage = response.message || "ID validation failed";
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.errorMessage = "Error validating ID";
        this.isLoading = false;
      },
      complete: () => this.isLoading = false
    });
  }

  loadTableMetadata() {
    this.isLoading = true;
    const metadataObservable = this.screenName
      ? this.metadataService.getScreenMetadata(this.screenName)
      : this.metadataService.getTableMetadata(this.tableName);

    metadataObservable.subscribe({
      next: (response: any) => {
        if (response?.data?.fieldName) {

          // ✅ Get columnNumber or default to 1
          this.columnCount = response.data.columnNumber || 1;

          // ✅ Order fields as usual
          const orderedFields = this.orderFieldsBasedOnFormDefinition(response.data.fieldName);
          // ⛔️ Skip "ID" so it doesn't take a layout slot
          const visibleFields = orderedFields.filter(
            field => field.fieldName?.toUpperCase() !== 'ID'
          );
          // ✅ Store isTenantBased if present
          if (response.data.isTenantBased) {
            this.isTenantBasedFlag = response.data.isTenantBased;
          }

          // ✅ Split fields into columns
          this.columns = this.distributeFieldsRoundRobin(visibleFields, this.columnCount);


          // ✅ Still keep old field assignment if you rely on it elsewhere
          this.fields = orderedFields;

          this.buildForm();
          this.fetchFormData();
          // Handle defaultFields if present in metadata response
          if (response.data.defaultFields && Array.isArray(response.data.defaultFields)) {
            this.populateDefaultFields(response.data.defaultFields);
          }
        } else {
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.isLoading = false;
      },
      complete: () => (this.isLoading = false),
    });
  }
  private distributeFieldsRoundRobin(fields: any[], columnCount: number): any[][] {
    const columns: any[][] = Array.from({ length: columnCount }, () => []);
    fields.forEach((field, index) => {
      const colIndex = index % columnCount;
      columns[colIndex].push(field);
    });
    return columns;
  }


  /**
   * Orders fields based on the formDefinition response structure
   * Fields with Group "fieldName" appear first, then other fields in their original order
   */
  private orderFieldsBasedOnFormDefinition(fields: any[]): any[] {
    if (!fields || !Array.isArray(fields)) {
      return fields;
    }

    // Separate fields with Group "fieldName" and preserve their order
    const fieldNameGroupFields = fields.filter(field => field.Group === "fieldName");

    // Get all other fields (non-fieldName group) and preserve their order
    const otherFields = fields.filter(field => field.Group !== "fieldName");

    // Combine: fieldName group fields first, then other fields in their original order
    return [
      ...fieldNameGroupFields,
      ...otherFields
    ];
  }

  buildForm() {
    const groupedFields: { [key: string]: FormArray } = {};

    // First, create all parent groups
    const parentGroups = this.getParentGroups();
    parentGroups.forEach(parentGroup => {
      if (!groupedFields[parentGroup]) {
        groupedFields[parentGroup] = this.fb.array([]);
        this.form.addControl(parentGroup, groupedFields[parentGroup]);
        this.addGroup(parentGroup);
      }
    });

    this.fields.forEach((field) => {
      if (field.fieldName !== "ID") {
        if (field.isMulti && !field.Group) {
          // Non-grouped multi-field
          const multiFieldArray = this.fb.array([this.createMultiField(field)]);
          this.form.addControl(field.fieldName, multiFieldArray);

          // Disable multi-field if noInput is true
          if (field.noInput) {
            multiFieldArray.disable({ emitEvent: false });
          }
        } else if (field.Group) {
          const parsed = this.parseGroupPath(field.Group);
          if (!parsed.isNested) {
            // Direct field of parent group - already handled in createGroup
          } else {
            // Nested group field - already handled in createGroup
          }
        } else {
          // Non-grouped regular field
          const validators = field.mandatory ? Validators.required : null;
          let control;
          switch (field.type) {
            case "boolean":
              control = this.fb.control(false, validators);
              break;
            case "date":
              control = this.fb.control(null, validators);
              break;
            default:
              control = this.fb.control("", validators);
              break;
          }
          this.form.addControl(field.fieldName, control);

          // Disable control if noInput is true
          if (field.noInput) {
            control.disable({ emitEvent: false });
          }


        }
      }
    });
  }




  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getFieldsForGroupPath(groupName).forEach((field) => {
        this.addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getFieldsForGroup(groupName).forEach((field) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getChildGroups(groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createGroup(`${groupName}|${childGroup}`)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  /**
   * Helper method to add a field to a FormGroup
   */
  private addFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else if (field.foreginKey) {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }


    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  getFieldsForGroup(groupName: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Get fields for a specific group path (supports nested groups with pipe notation)
   * @param groupPath - The full group path (e.g., "type|field")
   * @returns Array of fields belonging to this group path
   */
  getFieldsForGroupPath(groupPath: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupPath.trim());
  }

  /**
   * Parse group path to get parent and child group names
   * @param groupPath - The group path (e.g., "type|field")
   * @returns Object with parent and child group names
   */
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    // Trim the entire groupPath first to handle trailing spaces
    const trimmedGroupPath = groupPath.trim();

    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  /**
   * Get all unique parent groups
   */
  getParentGroups(): string[] {
    const parentGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent) {
          parentGroups.add(parsed.parent);
        }
      }
    });
    return Array.from(parentGroups);
  }

  /**
   * Get all child groups for a specific parent group
   */
  getChildGroups(parentGroup: string): string[] {
    const childGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent === parentGroup.trim() && parsed.child) {
          childGroups.add(parsed.child);
        }
      }
    });
    return Array.from(childGroups);
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  /**
   * Get nested group array using path notation
   * @param groupPath - Path like "type|field" for nested groups
   * @param parentIndex - Index of parent group instance
   */
  getNestedGroupArray(groupPath: string, parentIndex?: number): FormArray {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
      const parentArray = this.getGroupArray(parsed.parent);
      const parentGroup = parentArray.at(parentIndex) as FormGroup;
      return parentGroup.get(parsed.child) as FormArray;
    }
    return this.getGroupArray(groupPath);
  }

  addGroup(groupName: string, index?: number) {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);

    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }
  }

  /**
   * Add nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to insert at (optional)
   */
  addNestedGroup(groupPath: string, parentIndex: number, index?: number) {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
      const newGroup = this.createGroup(groupPath);

      if (index !== undefined) {
        nestedArray.insert(index + 1, newGroup);
      } else {
        nestedArray.push(newGroup);
      }
    }
  }

  removeGroup(groupName: string, index: number) {
    this.getGroupArray(groupName).removeAt(index);
  }

  /**
   * Remove nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to remove
   */
  removeNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    nestedArray.removeAt(index);
  }

  /**
   * Clone nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to clone
   */
  cloneNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    const groupToClone = nestedArray.at(index) as FormGroup;
    const clonedGroup = this.createGroup(groupPath);

    // Copy values from the original group to the cloned group
    Object.keys(groupToClone.controls).forEach(key => {
      const originalControl = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (originalControl && clonedControl) {
        if (originalControl instanceof FormArray) {
          // Handle FormArray cloning
          const originalArray = originalControl as FormArray;
          const clonedArray = clonedControl as FormArray;

          // Clear the default entry and copy all entries from original
          clonedArray.clear();
          originalArray.controls.forEach(control => {
            if (control instanceof FormGroup) {
              const newControl = this.fb.group({});
              Object.keys(control.controls).forEach(subKey => {
                newControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
              });
              clonedArray.push(newControl);
            }
          });
        } else {
          // Handle regular FormControl cloning
          clonedControl.setValue(originalControl.value);
        }
      }
    });

    nestedArray.insert(index + 1, clonedGroup);
  }

  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(groupName);
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return this.form.get(fieldName) as FormArray;
    }
  }

  addMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

    } catch (error) {
      // Handle error silently
    }
  }

  removeMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    const multiArray = this.getMultiArray(fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }

  isFirstFieldInGroup(field: any): boolean {
    return (
      this.fields.findIndex((f) => f.Group === field.Group) ===
      this.fields.indexOf(field)
    );
  }

  /**
   * Check if this is the first field in a parent group (for rendering group headers)
   */
  isFirstFieldInParentGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    // Find the first field that belongs to this parent group
    const firstFieldIndex = this.fields.findIndex((f) => {
      if (!f.Group) return false;
      const fParsed = this.parseGroupPath(f.Group);
      return fParsed.parent === parsed.parent;
    });

    return firstFieldIndex === this.fields.indexOf(field);
  }

  /**
   * Check if this is the first field in a nested group (for rendering nested group headers)
   */
  isFirstFieldInNestedGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.isNested || !parsed.child) return false;

    // Find the first field that belongs to this specific nested group path
    return (
      this.fields.findIndex((f) => f.Group && f.Group.trim() === field.Group.trim()) ===
      this.fields.indexOf(field)
    );
  }

  trackByFieldName(_index: number, field: any): string {
    return field.fieldName;
  }












  // Helper method to extract part before comma for tables API calls
  private extractTablesApiId(tableName?: string): string {
    const nameToUse = tableName || this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }

  fetchFormData() {
    this.isLoading = true;
    const id = this.form.get("ID")?.value;
    const tablesApiId = this.extractTablesApiId(this.tableName);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
    };
    this.http.get(apiUrl, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.populateForm(response.data);
        }
        // Handle defaultFields if present in response
        if (response && response.defaultFields && Array.isArray(response.defaultFields)) {
          this.populateDefaultFields(response.defaultFields);
        }
      },
      error: (error) => {
        this.errorMessage = "An error occurred while fetching data";
      },
      complete: () => this.isLoading = false
    });
  }

  // populateForm(data: any) {
  //   Object.keys(data).forEach(key => {
  //     const formControl = this.form.get(key);

  //     if (formControl instanceof FormArray && Array.isArray(data[key])) {
  //       const formArray = formControl as FormArray;
  //       formArray.clear(); // Clear existing controls in the FormArray

  //       if (this.fields.some(field => field.Group === key)) {
  //         // Handle Group fields 
  //         data[key].forEach((groupData: any) => {
  //           formArray.push(this.createGroup(key)); 
  //           (formArray.at(formArray.length - 1) as FormGroup).patchValue(groupData);
  //         });
  //       } else {
  //         // Handle multi-fields 
  //         const field = this.fields.find(field => field.fieldName === key);
  //         if (field) {
  //           // Create form groups in the FormArray
  //           for (let i = 0; i < data[key].length; i++) {
  //             formArray.push(this.createMultiField(field));
  //           }

  //           // Patch the values
  //           data[key].forEach((value: any, index: number) => {
  //             const newGroup = formArray.at(index) as FormGroup;

  //             // Check if the value is an object (for multi-fields with multiple properties)
  //             if (typeof value === 'object' && !Array.isArray(value)) {  
  //               newGroup.patchValue(value); 
  //             } else {
  //               // If the value is not an object, patch it to the field.fieldName
  //               newGroup.patchValue({ [field.fieldName]: value }); 
  //             }
  //           });
  //         }
  //       }
  //     } else if (formControl) {
  //       // For simple fields (not FormArray)
  //       const field = this.fields.find(field => field.fieldName === key);
  //       if (field && field.type === 'date' && typeof data[key] === 'string') {
  //         const parsedDate = new Date(data[key]);
  //         const dateOnly = parsedDate.toISOString().split('T')[0];
  //         if (!isNaN(parsedDate.getTime())) {
  //           formControl.setValue(dateOnly);
  //         } else {
  //           formControl.setValue(null);
  //         }
  //       } else if (field && field.type === 'date' && Array.isArray(data[key])) {
  //         // Handle the case where data[key] is an array of date strings (for multi-fields)
  //         const parsedDates = data[key].map(dateStr => {
  //           const parsedDate = new Date(dateStr);
  //           return !isNaN(parsedDate.getTime()) ? parsedDate : null;
  //         });
  //         formControl.setValue(parsedDates);
  //       } else {
  //         formControl.setValue(data[key]);
  //       }

  //       if (this.isViewMode) {
  //         formControl.disable(); // Disable the control AFTER setting the value
  //       }
  //     }
  //   });
  // }

  populateForm(data: any): void {
    Object.keys(data).forEach(key => {
      const formControl = this.form.get(key);

      if (formControl instanceof FormArray && Array.isArray(data[key])) {
        const formArray = formControl as FormArray;
        formArray.clear();

        if (this.fields.some(field => field.Group === key)) {
          // 🔷 Handle Grouped Fields (e.g., ledger)
          data[key].forEach((groupData: any, groupIndex: number) => {
            const groupForm = this.createGroup(key) as FormGroup;

            // Patch flat fields first (excluding nested arrays)
            const flatGroupData = { ...groupData };
            Object.keys(flatGroupData).forEach(nestedKey => {
              if (Array.isArray(flatGroupData[nestedKey])) {
                delete flatGroupData[nestedKey];
              }
            });
            groupForm.patchValue(flatGroupData);

            // 🔷 Handle nested multi-fields (e.g., denom)
            Object.keys(groupData).forEach(nestedKey => {
              const nestedValue = groupData[nestedKey];
              const nestedControl = groupForm.get(nestedKey);

              if (nestedControl instanceof FormArray && Array.isArray(nestedValue)) {
                nestedControl.clear();

                const nestedFieldMeta = this.fields.find(f =>
                  f.fieldName === nestedKey &&
                  f.Group?.startsWith(key)
                );

                if (nestedFieldMeta) {
                  nestedValue.forEach((item: any, index: number) => {
                    console.log(`📦 Pushing into [${key} > ${nestedKey}] index ${index}:`, item);

                    const nestedGroup = this.createMultiField2(nestedFieldMeta, item); // ← Pass item as second argument
                    nestedGroup.patchValue(item);
                    nestedControl.push(nestedGroup);
                  });
                }
              }
            });

            formArray.push(groupForm);
          });

        } else {
          // 🔶 Handle Flat Multi-Fields
          const field = this.fields.find(field => field.fieldName === key);
          if (field) {
            data[key].forEach((value: any, index: number) => {
              const multiGroup = this.createMultiField(field);
              if (typeof value === 'object' && !Array.isArray(value)) {
                multiGroup.patchValue(value);
              } else {
                multiGroup.patchValue({ [field.fieldName]: value });
              }
              formArray.push(multiGroup);
            });
          }
        }

      } else if (formControl) {
        // For simple fields (not FormArray)
        const field = this.fields.find(field => field.fieldName === key);
        if (field && field.type === 'date' && typeof data[key] === 'string') {
          const parsedDate = new Date(data[key]);
          const dateOnly = parsedDate.toISOString().split('T')[0];
          if (!isNaN(parsedDate.getTime())) {
            formControl.setValue(dateOnly);
          } else {
            formControl.setValue(null);
          }
        } else if (field && field.type === 'date' && Array.isArray(data[key])) {
          // Handle the case where data[key] is an array of date strings (for multi-fields)
          const parsedDates = data[key].map(dateStr => {
            const parsedDate = new Date(dateStr);
            return !isNaN(parsedDate.getTime()) ? parsedDate : null;
          });
          formControl.setValue(parsedDates);
        } else {
          formControl.setValue(data[key]);
        }

        if (this.isViewMode) {
          formControl.disable(); // Disable the control AFTER setting the value
        }
      }
    });
  }
  //populatedefualt fields 
  populateDefaultFields(defaultFields: any[]) {
    if (!Array.isArray(defaultFields)) return;
    defaultFields.forEach(item => {
      if (item && typeof item === 'object') {
        // Handle API structure: { defaultField, defaultValue }
        if ('defaultField' in item && 'defaultValue' in item) {
          const fieldName = item.defaultField;
          const defaultValue = item.defaultValue;
          const formControl = this.form.get(fieldName);
          if (formControl && defaultValue !== null && defaultValue !== undefined) {
            const wasDisabled = formControl.disabled;
            if (wasDisabled) formControl.enable({ emitEvent: false });
            const field = this.fields.find(f => f.fieldName === fieldName);
            if (field && field.type === 'date' && typeof defaultValue === 'string') {
              const parsedDate = new Date(defaultValue);
              if (!isNaN(parsedDate.getTime())) {
                const dateOnly = parsedDate.toISOString().split('T')[0];
                formControl.setValue(dateOnly);
              }
            } else if (field && field.type === 'boolean') {
              const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
              formControl.setValue(boolValue);
            } else if (field && (field.type === 'int' || field.type === 'double')) {
              const numValue = parseFloat(defaultValue);
              if (!isNaN(numValue)) {
                formControl.setValue(numValue);
              }
            } else {
              formControl.setValue(defaultValue);
            }
            if (wasDisabled) formControl.disable({ emitEvent: false });
          }
        } else {
          // Fallback: handle { fieldName: value } structure
          Object.keys(item).forEach(fieldName => {
            const defaultValue = item[fieldName];
            const formControl = this.form.get(fieldName);
            if (formControl && defaultValue !== null && defaultValue !== undefined) {
              const wasDisabled = formControl.disabled;
              if (wasDisabled) formControl.enable({ emitEvent: false });
              const field = this.fields.find(f => f.fieldName === fieldName);
              if (field && field.type === 'date' && typeof defaultValue === 'string') {
                const parsedDate = new Date(defaultValue);
                if (!isNaN(parsedDate.getTime())) {
                  const dateOnly = parsedDate.toISOString().split('T')[0];
                  formControl.setValue(dateOnly);
                }
              } else if (field && field.type === 'boolean') {
                const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
                formControl.setValue(boolValue);
              } else if (field && (field.type === 'int' || field.type === 'double')) {
                const numValue = parseFloat(defaultValue);
                if (!isNaN(numValue)) {
                  formControl.setValue(numValue);
                }
              } else {
                formControl.setValue(defaultValue);
              }
              if (wasDisabled) formControl.disable({ emitEvent: false });
            }
          });
        }
      }
    });
  }

  createMultiField2(fieldMeta: any, sampleData?: any): FormGroup {
    const groupFields: any = {};

    if (sampleData && typeof sampleData === 'object') {
      for (const key of Object.keys(sampleData)) {
        groupFields[key] = '';
      }
    } else {
      groupFields[fieldMeta.fieldName] = '';
    }

    return this.fb.group(groupFields);
  }

  goBack() {
    const id = this.form.get("ID")?.value;
    const tablesApiId = this.extractTablesApiId(this.tableName);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/force-unlock`;

    this.http.delete(apiUrl, { withCredentials: true }).subscribe({
      next: () => {
        // Record unlocked successfully
      },
      error: (error) => {
        this.cleanupForm();
      },
      complete: () => {
        this.cleanupForm();
      }
    });
  }

  private cleanupForm() {
    // First remove all controls from the form except ID
    Object.keys(this.form.controls).forEach(key => {
      if (key !== 'ID') {
        this.form.removeControl(key);
      }
    });

    // Reset the form
    this.form.reset();

    // Clear all fields and state
    this.fields = [];
    this.showInitialInput = true;
    this.isViewMode = false;
    this.submissionSuccess = false;
    this.validationResult = null;
    this.showValidation = false; // Reset validation state
    this.setFormReadonly(false);


  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  toggleViewMode() {
    this.isRowView = !this.isRowView;
  }

  // Form Actions Component Event Handlers
  onSubmissionSuccess(success: boolean) {
    this.submissionSuccess = success;
  }

  onErrorMessageChange(message: string) {
    this.errorMessage = message;
  }

  onIsLoadingChange(loading: boolean) {
    this.isLoading = loading;
  }

  onShowSuccessPopupChange(show: boolean) {
    this.showSuccessPopup = show;
  }

  onSuccessMessageChange(message: string) {
    this.successMessage = message;
  }

  onValidationResultChange(result: any) {
    this.validationResult = result;
  }

  onGoBackRequested() {
    this.goBack();
  }

  onSetFormReadonly(readonly: boolean) {
    this.setFormReadonly(readonly);
  }

  onPopulateForm(data: any) {
    this.populateForm(data);
  }

  onPopulateDefaultFields(fields: any[]) {
    this.populateDefaultFields(fields);
  }

  // Form Header Delegation Methods
  onFormSubmit() {
    this.formActions.onSubmit();
  }

  onFormValidate() {
    this.formActions.validateRecord();
  }

  onFormAuthorize() {
    this.formActions.authorizeRecord();
  }

  onFormReject() {
    this.formActions.onRejectRecord();
  }

  onFormDelete() {
    this.formActions.onDeleteRecord();
  }




  cloneGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Step 1: Add a new empty group using your existing method
    this.addGroup(groupName, index);

    // Step 2: Get the newly inserted group
    const clonedGroup = groupArray.at(index + 1) as FormGroup;

    // Step 3: Copy values (deep clone including nested FormArrays)
    Object.keys(groupToClone.controls).forEach(key => {
      const control = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (control instanceof FormArray && clonedControl instanceof FormArray) {
        clonedControl.clear();
        control.controls.forEach(c => {
          const clonedSubGroup = this.fb.group({});
          Object.keys((c as FormGroup).controls).forEach(subKey => {
            clonedSubGroup.addControl(subKey, this.fb.control((c as FormGroup).get(subKey)?.value));
          });
          clonedControl.push(clonedSubGroup);
        });
      } else {
        clonedControl?.setValue(control?.value);
      }
    });
  }



  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  onValidationChange(showValidation: boolean): void {
    this.showValidation = showValidation;
  }


  isIdValid(): boolean {
    const idValue = this.form.get('ID')?.value;
    return idValue && idValue.trim() !== '';
  }

  // Handle field value changes from child components
  onFieldValueChange(_event: {fieldName: string, value: any}): void {
    // Child component already handles form control update
    // This method can be used for additional logic if needed
  }

}

